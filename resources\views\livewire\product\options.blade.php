{{-- Product Options Component - Clean rebuild with proper Blade syntax --}}
<div>
    {{-- Product Title --}}
    <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight text-black">{{ $product->name }}</h1>

    {{-- Product Price Section --}}
    <div class="mt-3 sm:mt-4">
        <h2 class="sr-only">Product information</h2>
        <p class="text-2xl sm:text-3xl lg:text-4xl tracking-tight text-black">
            ₦{{ number_format($this->selectedVariant ? $this->selectedVariant->price : $product->getCurrentPrice(), 2) }}
        </p>
        @if($product->isOnSale() && $product->discount_price)
            <p class="text-lg sm:text-xl text-gray-500 line-through ml-2">₦{{ number_format($product->price, 2) }}</p>
        @endif
    </div>

    {{-- Product Information Grid --}}
    <div class="mt-4 space-y-4">
        {{-- Reviews and Rating --}}
        <div class="flex items-center">
            <div class="flex items-center">
                @if ($product->reviewCount() > 0)
                    @for ($i = 1; $i <= 5; $i++)
                        <svg class="h-5 w-5 flex-shrink-0 {{ $i <= $product->averageRating() ? 'text-yellow-400' : 'text-gray-300' }}" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10.868 2.884c.321-.662 1.215-.662 1.536 0l1.681 3.462 3.82.556c.734.107 1.03.998.494 1.512l-2.764 2.693.654 3.803c.124.722-.638 1.283-1.286.944L10 13.6l-3.416 1.795c-.648.34-1.41-.222-1.286-.944l.654-3.803-2.764-2.693c-.536-.514-.24-1.405.494-1.512l3.82-.556 1.681-3.462z" clip-rule="evenodd" />
                        </svg>
                    @endfor
                    <span class="ml-2 text-sm text-gray-600">{{ number_format($product->averageRating(), 1) }}</span>
                    <a href="#reviews" class="ml-3 text-sm font-medium text-gray-600 hover:text-black">{{ $product->reviewCount() }} {{ Str::plural('review', $product->reviewCount()) }}</a>
                @else
                    <span class="text-sm text-gray-500">No reviews yet</span>
                @endif
            </div>
        </div>

        {{-- Product Info Grid --}}
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
            {{-- Vendor --}}
            <div class="flex items-center">
                <span class="text-gray-500 font-medium">Vendor:</span>
                @if($product->vendor)
                    <a href="{{ route('vendors.storefront', $product->vendor->slug) }}" class="ml-2 font-medium text-gray-900 hover:text-black">{{ $product->vendor->shop_name }}</a>
                @else
                    <span class="ml-2 text-gray-400">No vendor assigned</span>
                @endif
            </div>

            {{-- Category --}}
            @if($product->category)
                <div class="flex items-center">
                    <span class="text-gray-500 font-medium">Category:</span>
                    <a href="{{ route('products.category', $product->category->slug) }}" class="ml-2 font-medium text-gray-900 hover:text-black">{{ $product->category->name }}</a>
                </div>
            @endif

            {{-- SKU --}}
            @if($product->sku)
                <div class="flex items-center">
                    <span class="text-gray-500 font-medium">SKU:</span>
                    <span class="ml-2 font-mono text-gray-700">{{ $product->sku }}</span>
                </div>
            @endif

            {{-- Stock Status --}}
            <div class="flex items-center">
                <span class="text-gray-500 font-medium">Availability:</span>
                @if($product->stock > 0)
                    <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        In Stock ({{ $product->stock }} available)
                    </span>
                @else
                    <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                        Out of Stock
                    </span>
                @endif
            </div>
        </div>
    </div>

    {{-- Product Description --}}
    @if($product->short_description)
        <p class="mt-6 text-gray-600 leading-relaxed">{{ $product->short_description }}</p>
    @elseif($product->description)
        <p class="mt-6 text-gray-600 leading-relaxed">{{ Str::limit($product->description, 200) }}</p>
    @endif

    {{-- Product Options Section --}}
    <div class="mt-6">
        {{-- Color Selection --}}
        @if($availableColors && $availableColors->isNotEmpty())
            <div class="mb-6">
                <h3 class="text-sm font-medium text-black mb-3">Color</h3>
                <div class="flex flex-wrap gap-3">
                    @foreach($availableColors as $color)
                        @if($color && $color->id)
                            <button type="button"
                                    wire:click="selectColor({{ $color->id }})"
                                    wire:loading.attr="disabled"
                                    wire:target="selectColor"
                                    class="group relative flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-200 disabled:opacity-50 {{ $selectedColorId === $color->id ? 'border-black shadow-lg scale-110' : 'border-gray-300 hover:border-gray-400' }}"
                                    style="background-color: {{ $color->hex_code ?? '#f3f4f6' }}">
                                <span class="sr-only">{{ $color->name ?? 'Color' }}</span>
                                @if($selectedColorId === $color->id)
                                    <svg class="w-4 h-4 text-white drop-shadow-lg" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                @endif
                                {{-- Loading indicator with CSS spinner --}}
                                <span wire:loading wire:target="selectColor" class="absolute inset-0 flex items-center justify-center">
                                    <div class="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                                </span>
                                {{-- Tooltip --}}
                                <div class="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                    {{ $color->name ?? 'Color' }}
                                </div>
                            </button>
                        @endif
                    @endforeach
                </div>
            </div>
        @endif
