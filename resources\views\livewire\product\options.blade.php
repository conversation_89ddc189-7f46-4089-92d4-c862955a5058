<div>
    <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight text-black">{{ $product->name }}</h1>

    <div class="mt-3 sm:mt-4">
        <h2 class="sr-only">Product information</h2>
        <p class="text-2xl sm:text-3xl lg:text-4xl tracking-tight text-black">₦{{ number_format($this->selectedVariant ? $this->selectedVariant->price : $product->getCurrentPrice(), 2) }}</p>
        @if($product->isOnSale() && $product->discount_price)
            <p class="text-lg sm:text-xl text-gray-500 line-through ml-2">₦{{ number_format($product->price, 2) }}</p>
        @endif
    </div>

    <div class="mt-4 space-y-4">
        <!-- Reviews and Rating -->
        <div class="flex items-center">
            <div class="flex items-center">
                @if ($product->reviewCount() > 0)
                    @for ($i = 1; $i <= 5; $i++)
                        <svg class="h-5 w-5 flex-shrink-0 {{ $i <= $product->averageRating() ? 'text-yellow-400' : 'text-gray-300' }}" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M10.868 2.884c.321-.662 1.215-.662 1.536 0l1.681 3.462 3.82.556c.734.107 1.03.998.494 1.512l-2.764 2.693.654 3.803c.124.722-.638 1.283-1.286.944L10 13.6l-3.416 1.795c-.648.34-1.41-.222-1.286-.944l.654-3.803-2.764-2.693c-.536-.514-.24-1.405.494-1.512l3.82-.556 1.681-3.462z" clip-rule="evenodd" />
                        </svg>
                    @endfor
                    <span class="ml-2 text-sm text-gray-600">{{ number_format($product->averageRating(), 1) }}</span>
                    <a href="#reviews" class="ml-3 text-sm font-medium text-gray-600 hover:text-black">{{ $product->reviewCount() }} {{ Str::plural('review', $product->reviewCount()) }}</a>
                @else
                    <span class="text-sm text-gray-500">No reviews yet</span>
                @endif
            </div>
        </div>

        <!-- Product Info Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
            <!-- Vendor -->
            <div class="flex items-center">
                <span class="text-gray-500 font-medium">Vendor:</span>
                @if($product->vendor)
                    <a href="{{ route('vendors.storefront', $product->vendor->slug) }}" class="ml-2 font-medium text-gray-900 hover:text-black">{{ $product->vendor->shop_name }}</a>
                @else
                    <span class="ml-2 text-gray-400">No vendor assigned</span>
                @endif
            </div>

            <!-- Category -->
            @if($product->category)
                <div class="flex items-center">
                    <span class="text-gray-500 font-medium">Category:</span>
                    <a href="{{ route('products.category', $product->category->slug) }}" class="ml-2 font-medium text-gray-900 hover:text-black">{{ $product->category->name }}</a>
                </div>
            @endif

            <!-- SKU -->
            @if($product->sku)
                <div class="flex items-center">
                    <span class="text-gray-500 font-medium">SKU:</span>
                    <span class="ml-2 font-mono text-gray-700">{{ $product->sku }}</span>
                </div>
            @endif

            <!-- Stock Status -->
            <div class="flex items-center">
                <span class="text-gray-500 font-medium">Availability:</span>
                @if($product->stock > 0)
                    <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        In Stock ({{ $product->stock }} available)
                    </span>
                @else
                    <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                        Out of Stock
                    </span>
                @endif
            </div>
        </div>
    </div>

    @if($product->short_description)
        <p class="mt-6 text-gray-600 leading-relaxed">{{ $product->short_description }}</p>
    @elseif($product->description)
        <p class="mt-6 text-gray-600 leading-relaxed">{{ Str::limit($product->description, 200) }}</p>
    @endif

    <!-- Product Options Section -->
    <div class="mt-6">
        @if($availableColors->isNotEmpty())
            <!-- Color Selection -->
            <div class="mb-6">
                <h3 class="text-sm font-medium text-black mb-3">Color</h3>
                <div class="flex flex-wrap gap-3">
                    @foreach($availableColors as $color)
                        @if($color && $color->id)
                            <button type="button"
                                    wire:click="selectColor({{ $color->id }})"
                                    wire:loading.attr="disabled"
                                    wire:target="selectColor"
                                    class="group relative flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-200 disabled:opacity-50 {{ $selectedColorId === $color->id ? 'border-black shadow-lg scale-110' : 'border-gray-300 hover:border-gray-400' }}"
                                    style="background-color: {{ $color->hex_code ?? '#f3f4f6' }}">
                                <span class="sr-only">{{ $color->name ?? 'Color' }}</span>
                                @if($selectedColorId === $color->id)
                                    <svg class="w-4 h-4 text-white drop-shadow-lg" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                @endif
                                <!-- Loading indicator -->
                                <span wire:loading wire:target="selectColor" class="absolute inset-0 flex items-center justify-center">
                                    <div class="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                                </span>
                                <!-- Tooltip -->
                                <div class="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                                    {{ $color->name ?? 'Color' }}
                                </div>
                            </button>
                        @endif
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Size Selection -->
        @if($availableSizes->isNotEmpty())
            <div class="mb-6">
                <h3 class="text-sm font-medium text-black mb-3">Size</h3>
                <div class="grid grid-cols-3 gap-2 sm:grid-cols-4 md:gap-3 lg:grid-cols-8">
                    @foreach($availableSizes as $size)
                        @if($size && $size->id)
                            <button type="button"
                                    wire:click="selectSize({{ $size->id }})"
                                    wire:loading.attr="disabled"
                                    wire:target="selectSize"
                                    class="group relative flex items-center justify-center rounded-md border py-2 px-2 text-xs sm:text-sm font-medium uppercase transition-all duration-200 focus:outline-none disabled:opacity-50
                                        {{ $selectedSizeId === $size->id
                                            ? 'border-black bg-black text-white scale-105 shadow-lg'
                                            : 'border-gray-300 text-gray-900 hover:bg-gray-50' }}"
                                    >
                                <span wire:loading.remove wire:target="selectSize">{{ $size->name ?? 'Size' }}</span>
                                <span wire:loading wire:target="selectSize" class="flex items-center">
                                    <div class="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full"></div>
                                </span>
                                @if($selectedSizeId === $size->id)
                                    <span class="pointer-events-none absolute -inset-px rounded-md" aria-hidden="true"></span>
                                @endif
                            </button>
                        @endif
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Variant Selection Warning -->
        @if(($availableColors->isNotEmpty() || $availableSizes->isNotEmpty()) && !$selectedVariantId)
            <div class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm text-yellow-800">
                        Please select
                        @if($availableColors->isNotEmpty() && !$selectedColorId)color@endif
                        @if($availableColors->isNotEmpty() && !$selectedColorId && $availableSizes->isNotEmpty() && !$selectedSizeId) and @endif
                        @if($availableSizes->isNotEmpty() && !$selectedSizeId)size@endif
                        to continue
                    </span>
                </div>
            </div>
        @endif

        <!-- Selected Variant Info -->
        @if($selectedVariantId)
            <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Selected:</span>
                    <span class="text-sm font-medium text-black">{{ $selectedVariant->display_name }}</span>
                </div>
                <div class="flex items-center justify-between mt-1">
                    <span class="text-sm text-gray-600">Stock:</span>
                    <span class="text-sm font-medium {{ $selectedVariant->stock_quantity > 10 ? 'text-green-600' : ($selectedVariant->stock_quantity > 0 ? 'text-yellow-600' : 'text-red-600') }}">
                        {{ $selectedVariant->stock_quantity }} available
                    </span>
                </div>
                @if($selectedVariant->sku)
                    <div class="flex items-center justify-between mt-1">
                        <span class="text-sm text-gray-600">SKU:</span>
                        <span class="text-sm font-mono text-gray-800">{{ $selectedVariant->sku }}</span>
                    </div>
                @endif
            </div>
        @endif

        <!-- Quantity and Add to Cart Section -->
        <div class="mt-6 sm:mt-8 flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
            <!-- Quantity Controls -->
            <div class="flex items-center border border-gray-300 rounded-md w-full sm:w-auto">
                <button type="button"
                        wire:click="decrementQuantity"
                        wire:loading.attr="disabled"
                        wire:target="decrementQuantity"
                        class="px-3 sm:px-4 py-2 sm:py-3 text-gray-500 hover:bg-gray-100 rounded-l-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                    <span wire:loading.remove wire:target="decrementQuantity">
                        <i class="fas fa-minus text-xs"></i>
                    </span>
                    <span wire:loading wire:target="decrementQuantity">
                        <div class="animate-spin w-3 h-3 border-2 border-current border-t-transparent rounded-full"></div>
                    </span>
                </button>
                <input type="text" wire:model="quantity" class="w-16 sm:w-20 text-center border-0 focus:ring-0 text-sm sm:text-base font-medium" readonly>
                <button type="button"
                        wire:click="incrementQuantity"
                        wire:loading.attr="disabled"
                        wire:target="incrementQuantity"
                        class="px-3 sm:px-4 py-2 sm:py-3 text-gray-500 hover:bg-gray-100 rounded-r-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                    <span wire:loading.remove wire:target="incrementQuantity">
                        <i class="fas fa-plus text-xs"></i>
                    </span>
                    <span wire:loading wire:target="incrementQuantity">
                        <div class="animate-spin w-3 h-3 border-2 border-current border-t-transparent rounded-full"></div>
                    </span>
                </button>
            </div>

            <!-- Add to Cart Button -->
            @php
                $hasVariants = $availableColors->isNotEmpty() || $availableSizes->isNotEmpty();
                $variantRequired = $hasVariants && !$selectedVariantId;
                $outOfStock = ($selectedVariant && !$selectedVariant->isAvailable()) || (!$selectedVariant && $product->stock < 1);
                $isDisabled = $variantRequired || $outOfStock;
            @endphp

            <button type="button"
                    wire:click="addToCart"
                    wire:loading.attr="disabled"
                    wire:target="addToCart"
                    @if($isDisabled) disabled @endif
                    class="flex-1 bg-black border border-transparent rounded-md py-3 sm:py-4 px-6 sm:px-8 flex items-center justify-center text-sm sm:text-base font-medium text-white hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200">
                <span wire:loading.remove wire:target="addToCart">
                    @if($variantRequired)
                        Select Options
                    @elseif($outOfStock)
                        Out of Stock
                    @else
                        Add to Cart
                    @endif
                </span>
                <span wire:loading wire:target="addToCart" class="flex items-center">
                    <div class="animate-spin -ml-1 mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                    Adding...
                </span>
            </button>
        </div>
    </div>

    <div class="mt-4">
        <button wire:click="toggleWishlist" class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-black bg-white hover:bg-gray-50">
            <svg class="h-5 w-5 mr-2 {{ $inWishlist ? 'text-red-500' : 'text-gray-400' }}" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
            </svg>
            <span wire:loading.remove wire:target="toggleWishlist">
                {{ $inWishlist ? 'Remove from Wishlist' : 'Add to Wishlist' }}
            </span>
            <span wire:loading wire:target="toggleWishlist">Loading...</span>
        </button>
    </div>
</div>
