<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Product;

class AddToCartButton extends Component
{

    public Product $product;
    public bool $wasAdded = false;

    public function addToCart()
    {
        // RACE CONDITION FIX: Check stock availability before adding to cart
        $requestedQuantity = 1;
        $cart = session()->get('cart', []);

        // Calculate total quantity if product already in cart
        if (isset($cart[$this->product->id])) {
            $requestedQuantity = $cart[$this->product->id]['quantity'] + 1;
        }

        // RACE CONDITION FIX: Verify stock availability
        if (!$this->product->hasSufficientStock($requestedQuantity)) {
            $availableStock = $this->product->getStockLevel();
            $this->dispatch('toast', message: "Sorry, only {$availableStock} units of {$this->product->name} are available.", type: 'error');
            return;
        }

        // SECURITY FIX: Validate product is active and available
        if (!$this->product->is_active) {
            $this->dispatch('toast', message: 'This product is no longer available.', type: 'error');
            return;
        }

        // SECURITY FIX: Use current price from database to prevent manipulation
        $currentPrice = $this->product->getCurrentPrice();

        if(isset($cart[$this->product->id])) {
            // RACE CONDITION FIX: Double-check stock before incrementing
            if (!$this->product->hasSufficientStock($cart[$this->product->id]['quantity'] + 1)) {
                $this->dispatch('toast', message: 'Insufficient stock to add more of this item.', type: 'error');
                return;
            }

            $cart[$this->product->id]['quantity']++;
            // SECURITY FIX: Update price to current price
            $cart[$this->product->id]['price'] = $currentPrice;
        } else {
            // Ensure we have a valid image URL
            $imageUrl = $this->product->image_url;
            if (empty($imageUrl) || (!filter_var($imageUrl, FILTER_VALIDATE_URL) && !file_exists(public_path(str_replace(url('/'), '', $imageUrl))))) {
                $imageUrl = asset('images/product-placeholder.svg');
            }
            
            $cart[$this->product->id] = [
                "name" => $this->product->name,
                "quantity" => 1,
                "price" => $currentPrice,
                "image" => $imageUrl,
                "vendor_id" => $this->product->vendor_id, // Add vendor ID for multi-vendor support
                "sku" => $this->product->sku,
            ];
        }

        session()->put('cart', $cart);

        $this->wasAdded = true;

        // Dispatch events to update the cart counter in the header
        $this->dispatch('cartUpdated');
        $this->dispatch('cart-updated-total', count($cart));

        // SECURITY FIX: Log cart additions for analytics and fraud detection
        \Log::info('Product added to cart', [
            'product_id' => $this->product->id,
            'product_name' => $this->product->name,
            'quantity' => $requestedQuantity,
            'price' => $currentPrice,
            'user_id' => auth()->id(),
            'session_id' => session()->getId(),
            'ip_address' => request()->ip()
        ]);

        // Show a success notification
        $this->dispatch('toast', message: "{$this->product->name} has been added to your cart.", type: 'success');
    }

    public function render()
    {
        return view('livewire.add-to-cart-button');
    }
}
