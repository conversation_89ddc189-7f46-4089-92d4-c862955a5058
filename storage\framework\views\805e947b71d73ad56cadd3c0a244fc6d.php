<div>
    
    <div class="relative">
        <form action="<?php echo e(route('products.search')); ?>" method="GET" wire:submit.prevent="performSearch">
            <div class="relative">
                <input
                    wire:model.live.debounce.300ms="query"
                    class="py-3 pr-12 pl-4 w-full text-base md:text-sm rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-black focus:border-black transition-all duration-200"
                    type="search"
                    name="query"
                    placeholder="Search products..."
                    autocomplete="off">
                <button type="submit" class="absolute top-0 right-0 h-full px-4 flex items-center justify-center min-w-[44px] min-h-[44px] rounded-r-full hover:bg-gray-50 transition-colors">
                    <i class="text-gray-500 fas fa-search text-lg"></i>
                </button>
            </div>
        </form>

        <!--[if BLOCK]><![endif]--><?php if(!empty($query) && $results->isNotEmpty()): ?>
            <div class="absolute z-50 mt-2 w-full max-h-80 bg-white rounded-lg border shadow-lg overflow-y-auto">
                <ul>
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $results; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="border-b border-gray-200 last:border-b-0 hover:bg-gray-50">
                            <a href="<?php echo e(route('products.show', $product->slug)); ?>" class="flex items-center p-4 min-h-[60px]">
                                <img src="<?php echo e($product->thumbnail_url); ?>" alt="<?php echo e($product->name); ?>" class="w-12 h-12 object-cover rounded-md flex-shrink-0">
                                <div class="ml-3 flex-1">
                                    <p class="font-semibold text-gray-800 text-sm"><?php echo e($product->name); ?></p>
                                    <p class="text-sm text-gray-600"><?php echo e($product->formatted_price); ?></p>
                                </div>
                            </a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </ul>
            </div>
        <?php elseif(!empty($query)): ?>
            <div class="absolute z-50 p-4 mt-2 w-full bg-white rounded-lg border shadow-lg">
                <p class="text-gray-600 text-sm">No results found for "<?php echo e($query); ?>".</p>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Documents\brandifyng\resources\views/livewire/search.blade.php ENDPATH**/ ?>