<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, maximum-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="theme-color" content="#ffffff">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">

    <title><?php echo e($title ?? config('app.name', 'Brandify')); ?></title>

    <!-- Vite Assets -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
    <link href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap" rel="stylesheet" />

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Animate.css for UI effects -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css" />
    <?php echo $__env->yieldPushContent('styles'); ?>

</head>
<body class="font-sans antialiased text-gray-900 bg-white">

<!-- Skip Navigation Link for Accessibility -->
<a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-black text-white px-4 py-2 rounded-md z-50">
    Skip to main content
</a>

<!-- Navigation -->
<header x-data="{ mobileMenuOpen: false, userMenuOpen: false }" class="fixed top-0 right-0 left-0 z-50 bg-white shadow-sm">
    <nav class="container px-4 mx-auto sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-14 sm:h-16">
            <!-- Logo -->
            <div class="flex-shrink-0">
                <a href="<?php echo e(route('home')); ?>" class="flex items-center">
                    <div class="flex items-center space-x-2">
                        <svg class="h-6 w-6 text-black" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" /></svg>
                        <span class="font-semibold text-lg sm:text-xl"><?php echo e(config('app.name', 'Laravel')); ?></span>
                    </div>
                </a>
            </div>

            <!-- Desktop Menu -->
            <div class="hidden lg:flex lg:items-center lg:space-x-8">
                <a href="<?php echo e(route('home')); ?>" class="font-medium text-gray-700 hover:text-black">Home</a>
                <a href="<?php echo e(route('products.index')); ?>" class="font-medium text-gray-700 hover:text-black">Shop</a>
                <a href="<?php echo e(route('about')); ?>" class="font-medium text-gray-700 hover:text-black">About</a>
                <a href="<?php echo e(route('contact')); ?>" class="font-medium text-gray-700 hover:text-black">Contact</a>
            </div>

            <!-- Right side icons & search -->
            <div class="flex items-center space-x-2 sm:space-x-4">
                <!-- Livewire Search Component - Hidden on small screens, shown on medium+ -->
                <div class="hidden md:block">
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('search');

$__html = app('livewire')->mount($__name, $__params, 'lw-969681406-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                </div>

                <!-- Essential Icons - Always visible -->
                <div class="flex items-center space-x-2 sm:space-x-3">
                    <?php if(auth()->guard()->check()): ?>
                        <!-- User Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="p-2.5 text-gray-600 hover:text-black focus:outline-none rounded-full hover:bg-gray-100 transition-all duration-200 min-w-[44px] min-h-[44px] flex items-center justify-center">
                                <i class="text-lg sm:text-xl fa-regular fa-user"></i>
                            </button>
                            <div x-show="open" @click.away="open = false" class="absolute right-0 z-50 py-1 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200"
                                x-transition:enter="transition ease-out duration-200"
                                x-transition:enter-start="transform opacity-0 scale-95"
                                x-transition:enter-end="transform opacity-100 scale-100"
                                x-transition:leave="transition ease-in duration-75"
                                x-transition:leave-start="transform opacity-100 scale-100"
                                x-transition:leave-end="transform opacity-0 scale-95"
                                style="display: none;">
                                <span class="block px-4 py-2 text-sm text-gray-700 font-medium"><?php echo e(auth()->user()->name); ?></span>
                                <div class="border-t border-gray-100"></div>
                                <?php if(optional(auth()->user())->isAdmin()): ?>
                                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors min-h-[44px] flex items-center">Admin Dashboard</a>
                                <?php endif; ?>
                                <?php if(optional(auth()->user())->isVendor()): ?>
                                    <a href="<?php echo e(route('vendor.dashboard')); ?>" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors min-h-[44px] flex items-center">Vendor Dashboard</a>
                                <?php endif; ?>
                                <a href="<?php echo e(route('wishlist.index')); ?>" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors min-h-[44px] flex items-center">My Wishlist</a>
                                <a href="<?php echo e(route('orders.index')); ?>" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors min-h-[44px] flex items-center">My Orders</a>
                                <div class="border-t border-gray-100"></div>
                                <form method="POST" action="<?php echo e(route('logout')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="block px-4 py-3 w-full text-sm text-left text-gray-700 hover:bg-gray-100 transition-colors min-h-[44px] flex items-center">Logout</button>
                                </form>
                            </div>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" class="p-2.5 text-gray-600 hover:text-black rounded-full hover:bg-gray-100 transition-all duration-200 min-w-[44px] min-h-[44px] flex items-center justify-center" title="Login">
                            <i class="text-lg sm:text-xl fa-regular fa-user"></i>
                        </a>
                    <?php endif; ?>

                    <!-- Wishlist - Hidden on very small screens -->
                    <a href="<?php echo e(route('wishlist.index')); ?>" class="hidden sm:flex p-2.5 text-gray-600 hover:text-black rounded-full hover:bg-gray-100 transition-all duration-200 min-w-[44px] min-h-[44px] items-center justify-center" title="Wishlist">
                        <i class="text-lg sm:text-xl fa-regular fa-heart"></i>
                    </a>

                    <!-- Cart Counter - Always visible -->
                    <div class="p-1">
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('cart-counter');

$__html = app('livewire')->mount($__name, $__params, 'lw-969681406-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    </div>
                </div>

                <!-- Mobile Menu Button -->
                <div class="flex lg:hidden ml-2">
                    <button @click="mobileMenuOpen = !mobileMenuOpen"
                            aria-expanded="false"
                            :aria-expanded="mobileMenuOpen"
                            aria-controls="mobile-menu"
                            class="inline-flex justify-center items-center p-2.5 text-gray-600 rounded-md hover:text-black hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-black transition-all duration-200 min-w-[44px] min-h-[44px]">
                        <span class="sr-only">Open main menu</span>
                        <i class="text-xl fa-solid fa-bars" x-show="!mobileMenuOpen"></i>
                        <i class="text-xl fa-solid fa-xmark" x-show="mobileMenuOpen"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu - Optimized animations for performance -->
        <div x-show="mobileMenuOpen"
             id="mobile-menu"
             role="navigation"
             aria-label="Mobile navigation menu"
             class="lg:hidden bg-white border-t border-gray-200 shadow-lg"
            x-transition:enter="transition ease-out duration-150"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-100"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            style="display: none;">

            <!-- Mobile Search - Only shown on small screens -->
            <div class="px-4 py-4 border-b border-gray-200 md:hidden">
                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('search');

$__html = app('livewire')->mount($__name, $__params, 'lw-969681406-2', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
            </div>

            <!-- Navigation Links -->
            <div class="pt-2 pb-3 space-y-0">
                <a href="<?php echo e(route('home')); ?>" class="flex items-center py-4 px-4 text-base font-medium text-gray-600 border-l-4 border-transparent hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 transition-all duration-200 min-h-[56px]">
                    <i class="fa-solid fa-home w-5 mr-3 text-gray-400"></i>Home
                </a>
                <a href="<?php echo e(route('products.index')); ?>" class="flex items-center py-4 px-4 text-base font-medium text-gray-600 border-l-4 border-transparent hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 transition-all duration-200 min-h-[56px]">
                    <i class="fa-solid fa-store w-5 mr-3 text-gray-400"></i>Shop
                </a>
                <a href="<?php echo e(route('about')); ?>" class="flex items-center py-4 px-4 text-base font-medium text-gray-600 border-l-4 border-transparent hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 transition-all duration-200 min-h-[56px]">
                    <i class="fa-solid fa-info-circle w-5 mr-3 text-gray-400"></i>About
                </a>
                <a href="<?php echo e(route('contact')); ?>" class="flex items-center py-4 px-4 text-base font-medium text-gray-600 border-l-4 border-transparent hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 transition-all duration-200 min-h-[56px]">
                    <i class="fa-solid fa-envelope w-5 mr-3 text-gray-400"></i>Contact
                </a>

                <!-- Wishlist link for small screens -->
                <a href="<?php echo e(route('wishlist.index')); ?>" class="flex items-center py-4 px-4 text-base font-medium text-gray-600 border-l-4 border-transparent hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 transition-all duration-200 sm:hidden min-h-[56px]">
                    <i class="fa-regular fa-heart w-5 mr-3 text-gray-400"></i>My Wishlist
                </a>
            </div>

            <!-- User Section -->
            <div class="pt-4 pb-3 border-t border-gray-200">
                <?php if(auth()->guard()->check()): ?>
                    <div class="flex items-center px-4 py-3">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                                <i class="fa-regular fa-user text-gray-600 text-lg"></i>
                            </div>
                        </div>
                        <div class="ml-3">
                            <div class="text-base font-medium text-gray-800"><?php echo e(optional(auth()->user())->name); ?></div>
                            <div class="text-sm font-medium text-gray-500"><?php echo e(optional(auth()->user())->email); ?></div>
                        </div>
                    </div>
                    <div class="mt-3 space-y-0">
                        <?php if(optional(auth()->user())->isAdmin()): ?>
                            <a href="<?php echo e(route('admin.dashboard')); ?>" class="flex items-center px-4 py-4 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 transition-colors min-h-[56px]">
                                <i class="fa-solid fa-cog w-5 mr-3 text-gray-400"></i>Admin Dashboard
                            </a>
                        <?php endif; ?>
                        <?php if(optional(auth()->user())->isVendor()): ?>
                            <a href="<?php echo e(route('vendor.dashboard')); ?>" class="flex items-center px-4 py-4 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 transition-colors min-h-[56px]">
                                <i class="fa-solid fa-chart-line w-5 mr-3 text-gray-400"></i>Vendor Dashboard
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo e(route('orders.index')); ?>" class="flex items-center px-4 py-4 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 transition-colors min-h-[56px]">
                            <i class="fa-solid fa-box w-5 mr-3 text-gray-400"></i>My Orders
                        </a>
                        <a href="<?php echo e(route('wishlist.index')); ?>" class="hidden sm:flex items-center px-4 py-4 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 transition-colors min-h-[56px]">
                            <i class="fa-regular fa-heart w-5 mr-3 text-gray-400"></i>My Wishlist
                        </a>
                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="flex items-center px-4 py-4 w-full text-base font-medium text-left text-gray-500 hover:text-gray-800 hover:bg-gray-100 transition-colors min-h-[56px]">
                                <i class="fa-solid fa-sign-out-alt w-5 mr-3 text-gray-400"></i>Logout
                            </button>
                        </form>
                    </div>
                <?php else: ?>
                    <div class="space-y-0 px-4">
                        <a href="<?php echo e(route('login')); ?>" class="flex items-center py-4 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors min-h-[56px]">
                            <i class="fa-solid fa-sign-in-alt w-5 mr-3 text-gray-400"></i>Login
                        </a>
                        <a href="<?php echo e(route('register')); ?>" class="flex items-center py-4 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors min-h-[56px]">
                            <i class="fa-solid fa-user-plus w-5 mr-3 text-gray-400"></i>Register
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </nav>
</header>

    <!-- Main Content with Standardized Navbar Spacing -->
    <main id="main-content" class="pt-16 sm:pt-20 min-h-screen">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Flash Messages -->
            <?php if(session('success')): ?>
                <div class="mb-6 p-4 bg-green-50 border border-green-200 text-green-800 rounded-lg">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="mb-6 p-4 bg-red-50 border border-red-200 text-red-800 rounded-lg">
                    <?php echo e(session('error')); ?>

                </div>
            <?php endif; ?>

            <?php if(session('warning')): ?>
                <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-lg">
                    <?php echo e(session('warning')); ?>

                </div>
            <?php endif; ?>

            <?php if(isset($slot)): ?>
                <?php echo e($slot); ?>

            <?php else: ?>
                <?php echo $__env->yieldContent('content'); ?>
            <?php endif; ?>
        </div>
    </main>


    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>

    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('product.quick-view', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-969681406-3', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

    
    <?php if (isset($component)) { $__componentOriginale5bc9b34dd139a393f71cdc403b71855 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale5bc9b34dd139a393f71cdc403b71855 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.notifications','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('notifications'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale5bc9b34dd139a393f71cdc403b71855)): ?>
<?php $attributes = $__attributesOriginale5bc9b34dd139a393f71cdc403b71855; ?>
<?php unset($__attributesOriginale5bc9b34dd139a393f71cdc403b71855); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale5bc9b34dd139a393f71cdc403b71855)): ?>
<?php $component = $__componentOriginale5bc9b34dd139a393f71cdc403b71855; ?>
<?php unset($__componentOriginale5bc9b34dd139a393f71cdc403b71855); ?>
<?php endif; ?>

    
    
    <script src="<?php echo e(asset('js/slider-config.js')); ?>"></script>
    <?php echo $__env->yieldPushContent('scripts'); ?>

    <?php echo $__env->make('layouts._footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            document.querySelectorAll('a[href="#"]').forEach(function (anchor) {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    alert('This feature is currently under development and will be available soon.');
                });
            });
        });
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Documents\brandifyng\resources\views/layouts/app.blade.php ENDPATH**/ ?>