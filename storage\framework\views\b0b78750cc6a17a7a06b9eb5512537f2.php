<?php $__env->startSection('content'); ?>
    <!-- Main Content - Uses standardized navbar spacing from layout -->
    <div class="mx-auto max-w-7xl">
        <!-- Hero Section with Split Layout - Mobile: Slider above text, Desktop: Side by side -->
        <section class="relative min-h-[400px] sm:min-h-[450px] md:min-h-[400px] lg:min-h-[480px] bg-gray-900 overflow-hidden rounded-xl shadow-2xl mb-6 sm:mb-8 lg:mb-12" aria-label="Hero banner with featured products">
            <!-- Content Grid - Mobile: flex-col-reverse (slider first), Desktop: grid -->
            <div class="relative z-10 flex flex-col-reverse h-full md:grid md:grid-cols-2">
                <!-- Text Content - Mobile: Bottom half, Desktop: Left side -->
                <div class="flex flex-col items-start justify-center h-1/2 md:h-full min-h-[200px] sm:min-h-[225px] md:min-h-[400px] lg:min-h-[480px] p-6 sm:p-8 md:p-10 lg:p-12 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-700">
                    <div class="max-w-lg w-full">
                        <h1 class="mb-3 sm:mb-4 text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-extrabold leading-tight text-white">
                            Elevate Your Wardrobe
                            <span class="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600 mt-1">with Exclusive Fashion</span>
                        </h1>
                        <p class="mb-4 sm:mb-6 text-sm sm:text-base md:text-lg text-gray-300 leading-relaxed">
                            Explore exclusive collections from Nigeria's most celebrated designers. Your journey into authentic, high-fashion starts here.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto">
                            <a href="<?php echo e(route('products.index')); ?>" class="w-full sm:w-auto text-center px-6 sm:px-8 py-3 sm:py-4 font-bold text-gray-900 bg-white rounded-full transition duration-300 ease-in-out transform hover:bg-gray-100 hover:scale-105 shadow-lg text-sm sm:text-base">
                                Shop Now
                            </a>
                            <a href="<?php echo e(route('about')); ?>" class="w-full sm:w-auto text-center px-6 sm:px-8 py-3 sm:py-4 font-bold text-white border-2 border-white rounded-full transition duration-300 ease-in-out transform hover:bg-white hover:text-gray-900 hover:scale-105 text-sm sm:text-base">
                                Learn More
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Image Slider - Mobile: Top half, Desktop: Right side -->
                <?php if(!empty($sliderBanners) && count($sliderBanners) > 0): ?>
                <div class="relative h-1/2 md:h-full min-h-[200px] sm:min-h-[225px] md:min-h-[400px] lg:min-h-[480px] flex flex-col justify-center">
                    <!-- Slider Background (Simple PHP Loop for reliability) -->
                    <?php $slideCount = count($sliderBanners); ?>
                    <div class="absolute top-0 left-0 right-0 bottom-0 z-0">
                        <?php $__currentLoopData = $sliderBanners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $idx => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div style="background-image: url('<?php echo e($banner); ?>'); background-size: cover; background-position: center;"
                                 class="absolute top-0 left-0 right-0 bottom-0 w-full h-full bg-cover bg-center transition-opacity duration-1000 <?php if($idx === 0): ?> opacity-100 <?php else: ?> opacity-0 <?php endif; ?> slider-image"
                                 data-slide="<?php echo e($idx); ?>">
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <!-- Transparent Overlay Effect -->
                    <div class="absolute top-0 left-0 right-0 bottom-0 bg-gradient-to-l from-transparent via-black/10 to-black/30 z-10"></div>

                    <!-- Slider Indicators -->
                    <div class="absolute bottom-4 sm:bottom-6 left-1/2 transform -translate-x-1/2 z-20">
                        <div class="flex space-x-2">
                            <?php for($idx = 0; $idx < $slideCount; $idx++): ?>
                                <button class="w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-all duration-300 hover:bg-white/80 bg-white/50 <?php if($idx === 0): ?> bg-white <?php endif; ?> slider-dot" data-slide="<?php echo e($idx); ?>"></button>
                            <?php endfor; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </section>

        <!-- Featured Products Section with Tabs -->
        <?php if($bestSellers->isNotEmpty() || $newArrivals->isNotEmpty()): ?>
            <section class="my-6 sm:my-8 lg:my-12" x-data="{ activeTab: 'bestsellers' }" aria-labelledby="featured-products-heading">
                <!-- Section Header -->
                <header class="text-center mb-6 sm:mb-8">
                    <h2 id="featured-products-heading" class="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Featured Products</h2>
                    <p class="text-gray-600 text-sm sm:text-base mb-6">Discover our most popular items and latest arrivals</p>

                    <!-- Tab Navigation -->
                    <div class="inline-flex bg-gray-100 rounded-full p-1 shadow-inner" role="tablist" aria-label="Product categories">
                        <?php if($bestSellers->isNotEmpty()): ?>
                            <button @click="activeTab = 'bestsellers'"
                                    :class="activeTab === 'bestsellers' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'"
                                    :aria-selected="activeTab === 'bestsellers'"
                                    role="tab"
                                    aria-controls="bestsellers-panel"
                                    id="bestsellers-tab"
                                    class="px-4 sm:px-6 py-2 sm:py-3 rounded-full font-semibold text-sm sm:text-base transition-all duration-300 transform hover:scale-105 min-h-[44px] focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    Best Sellers
                                </span>
                            </button>
                        <?php endif; ?>

                        <?php if($newArrivals->isNotEmpty()): ?>
                            <button @click="activeTab = 'newarrivals'"
                                    :class="activeTab === 'newarrivals' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'"
                                    :aria-selected="activeTab === 'newarrivals'"
                                    role="tab"
                                    aria-controls="newarrivals-panel"
                                    id="newarrivals-tab"
                                    class="px-4 sm:px-6 py-2 sm:py-3 rounded-full font-semibold text-sm sm:text-base transition-all duration-300 transform hover:scale-105 min-h-[44px] focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                    New Arrivals
                                </span>
                            </button>
                        <?php endif; ?>
                    </div>
                </header>

                <!-- Tab Content -->
                <div class="relative">
                    <!-- Best Sellers Tab -->
                    <?php if($bestSellers->isNotEmpty()): ?>
                        <div x-show="activeTab === 'bestsellers'"
                             x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0 transform translate-y-4"
                             x-transition:enter-end="opacity-100 transform translate-y-0"
                             x-transition:leave="transition ease-in duration-200"
                             x-transition:leave-start="opacity-100 transform translate-y-0"
                             x-transition:leave-end="opacity-0 transform translate-y-4"
                             role="tabpanel"
                             id="bestsellers-panel"
                             aria-labelledby="bestsellers-tab"
                             class="grid grid-cols-2 gap-3 sm:gap-4 md:gap-6 lg:grid-cols-4 lg:gap-8">
                            <?php $__currentLoopData = $bestSellers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('product-card', ['product' => $product]);

$__html = app('livewire')->mount($__name, $__params, 'best-seller-'.$product->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>

                    <!-- New Arrivals Tab -->
                    <?php if($newArrivals->isNotEmpty()): ?>
                        <div x-show="activeTab === 'newarrivals'"
                             x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0 transform translate-y-4"
                             x-transition:enter-end="opacity-100 transform translate-y-0"
                             x-transition:leave="transition ease-in duration-200"
                             x-transition:leave-start="opacity-100 transform translate-y-0"
                             x-transition:leave-end="opacity-0 transform translate-y-4"
                             role="tabpanel"
                             id="newarrivals-panel"
                             aria-labelledby="newarrivals-tab"
                             class="grid grid-cols-2 gap-3 sm:gap-4 md:gap-6 lg:grid-cols-4 lg:gap-8">
                            <?php $__currentLoopData = $newArrivals; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('product-card', ['product' => $product]);

$__html = app('livewire')->mount($__name, $__params, 'new-arrival-'.$product->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- View All Button -->
                <div class="text-center mt-8 sm:mt-10">
                    <a href="<?php echo e(route('products.index')); ?>"
                       class="inline-flex items-center px-6 sm:px-8 py-3 sm:py-4 bg-black text-white font-semibold rounded-full hover:bg-gray-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl text-sm sm:text-base">
                        <span>View All Products</span>
                        <svg class="w-4 h-4 sm:w-5 sm:h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </header>
            </section>
        <?php endif; ?>

        <!-- Featured Brands Section -->
        <?php if($featuredBrands->isNotEmpty()): ?>
            <section class="mb-6 sm:mb-8 lg:mb-12" aria-labelledby="featured-brands-heading">
                <h2 id="featured-brands-heading" class="mb-6 sm:mb-8 text-2xl sm:text-3xl lg:text-4xl font-bold text-center text-gray-900">Featured Brands</h2>
                <div class="grid grid-cols-1 gap-4 sm:gap-6 md:grid-cols-2 lg:grid-cols-3 lg:gap-8">
                    <?php $__currentLoopData = $featuredBrands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="p-4 sm:p-6 text-center bg-white rounded-lg border border-gray-200 shadow-md hover:shadow-lg transition-shadow duration-300">
                            <img src="<?php echo e($brand->logo_url ?? asset('storage/brand-placeholder.png')); ?>"
                                 alt="<?php echo e($brand->name); ?> Logo"
                                 class="object-contain mx-auto mb-3 sm:mb-4 w-24 h-12 sm:w-32 sm:h-16">
                            <h3 class="text-lg sm:text-xl font-bold text-gray-900 mb-2"><?php echo e($brand->name); ?></h3>
                            <?php if($brand->type === 'vendor'): ?>
                                <a href="<?php echo e(route('vendors.storefront', $brand->slug)); ?>"
                                   class="inline-block mt-2 px-4 py-2 text-sm font-medium text-white bg-black rounded-md hover:bg-gray-800 transition-colors duration-200">Shop Now</a>
                            <?php else: ?>
                                <a href="<?php echo e(route('products.index', ['brand' => $brand->slug])); ?>"
                                   class="inline-block mt-2 px-4 py-2 text-sm font-medium text-white bg-black rounded-md hover:bg-gray-800 transition-colors duration-200">View Products</a>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </section>
        <?php endif; ?>

        <!-- Newsletter Section -->
        <div class="mb-8 sm:mb-12 lg:mb-16 flex justify-center">
            <div class="w-full max-w-2xl mx-4 sm:mx-6 lg:mx-8 p-6 sm:p-8 bg-white rounded-lg shadow-lg border border-gray-200 text-center">
                <h2 class="mb-3 sm:mb-4 text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Join Our Newsletter</h2>
                <p class="mb-4 sm:mb-6 text-sm sm:text-base text-gray-600 leading-relaxed">Stay updated with the latest products, exclusive offers, and fashion news.</p>
                <form class="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-stretch sm:items-center" method="POST" action="#" onsubmit="event.preventDefault(); alert('Newsletter subscription feature coming soon!');">
                    <?php echo csrf_field(); ?>
                    <input type="email"
                           name="email"
                           class="px-4 py-3 w-full sm:flex-1 text-gray-800 bg-white rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-black focus:border-black transition-all duration-200"
                           placeholder="Your email address"
                           required>
                    <button class="px-6 py-3 font-semibold text-white bg-black rounded-md transition-all duration-200 hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transform hover:scale-105 whitespace-nowrap"
                            type="submit">
                        Subscribe
                    </button>
                </form>
            </div>
        </div>
    </div>
    <?php $__env->startPush('scripts'); ?>
    <!-- Hero Slider JavaScript -->
    <script src="<?php echo e(asset('js/simple-slider.js')); ?>"></script>

    <!-- Cart AJAX JavaScript -->
    <!-- <script src="<?php echo e(asset('js/cart.js')); ?>"></script> -->
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\brandifyng\resources\views/welcome-bw.blade.php ENDPATH**/ ?>