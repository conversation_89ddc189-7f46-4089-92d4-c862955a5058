<div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">

            
            <nav class="mb-4 text-sm text-gray-500" aria-label="Breadcrumb">
                <ol class="list-none p-0 inline-flex">
                    <li class="flex items-center">
                        <a href="<?php echo e(route('home')); ?>" class="hover:text-gray-900">Home</a>
                        <svg class="fill-current w-3 h-3 mx-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"/></svg>
                    </li>
                    <li class="flex items-center">
                        <a href="<?php echo e(route('products.index')); ?>" class="hover:text-gray-900">Shop</a>
                        <svg class="fill-current w-3 h-3 mx-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"/></svg>
                    </li>
                    <!--[if BLOCK]><![endif]--><?php if($product->category): ?>
                        <li class="flex items-center">
                            <a href="<?php echo e(route('products.category', $product->category->slug)); ?>" class="hover:text-gray-900"><?php echo e($product->category->name); ?></a>
                            <svg class="fill-current w-3 h-3 mx-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"/></svg>
                        </li>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    <li>
                        <span class="text-gray-400"><?php echo e($product->name); ?></span>
                    </li>
                </ol>
            </nav>

            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12">

                
                <div class="order-1">
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('product.gallery', ['product' => $product]);

$__html = app('livewire')->mount($__name, $__params, 'lw-3607487015-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                </div>

                
                <div class="order-2">
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('product.options', ['product' => $product]);

$__html = app('livewire')->mount($__name, $__params, 'lw-3607487015-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                </div>

            </div>

            
            <div class="mt-12 lg:mt-16">
                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('product.tabs', ['product' => $product]);

$__html = app('livewire')->mount($__name, $__params, 'lw-3607487015-2', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
            </div>

            
            <!--[if BLOCK]><![endif]--><?php if($relatedProducts && $relatedProducts->count() > 0): ?>
                <div class="mt-16 lg:mt-20">
                    <div class="border-t border-gray-200 pt-12">
                        <h2 class="text-2xl font-bold text-gray-900 mb-8">You might also like</h2>
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 lg:gap-6">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $relatedProducts->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedProduct): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="group">
                                    <a href="<?php echo e(route('products.show', $relatedProduct->slug)); ?>" class="block">
                                        <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden group-hover:opacity-75 transition-opacity">
                                            <img src="<?php echo e($relatedProduct->image_url); ?>"
                                                 alt="<?php echo e($relatedProduct->name); ?>"
                                                 class="w-full h-full object-center object-cover"
                                                 onerror="this.onerror=null; this.src='<?php echo e(asset('images/product-placeholder.svg')); ?>';">
                                        </div>
                                        <div class="mt-4">
                                            <h3 class="text-sm font-medium text-gray-900 group-hover:text-black">
                                                <?php echo e(Str::limit($relatedProduct->name, 50)); ?>

                                            </h3>
                                            <div class="mt-1 flex items-center space-x-2">
                                                <!--[if BLOCK]><![endif]--><?php if($relatedProduct->isOnSale()): ?>
                                                    <span class="text-sm font-medium text-gray-900">₦<?php echo e(number_format($relatedProduct->discount_price, 0)); ?></span>
                                                    <span class="text-sm text-gray-500 line-through">₦<?php echo e(number_format($relatedProduct->price, 0)); ?></span>
                                                <?php else: ?>
                                                    <span class="text-sm font-medium text-gray-900">₦<?php echo e(number_format($relatedProduct->price, 0)); ?></span>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            
            <!--[if BLOCK]><![endif]--><?php if($relatedProducts->count() > 0): ?>
                <div class="mt-12 sm:mt-16">
                    <h3 class="text-xl sm:text-2xl font-bold mb-4 sm:mb-6 text-gray-900">Related Products</h3>
                    <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $relatedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedProduct): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('product-card', ['product' => $relatedProduct]);

$__html = app('livewire')->mount($__name, $__params, $relatedProduct->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            
            <!--[if BLOCK]><![endif]--><?php if($recentlyViewedProducts->count() > 0): ?>
                <div class="mt-12 sm:mt-16">
                    <h3 class="text-xl sm:text-2xl font-bold mb-4 sm:mb-6 text-gray-900">Recently Viewed</h3>
                    <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $recentlyViewedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recentProduct): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('product-card', ['product' => $recentProduct]);

$__html = app('livewire')->mount($__name, $__params, $recentProduct->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Documents\brandifyng\resources\views/livewire/product/show.blade.php ENDPATH**/ ?>