<?php

namespace App\Livewire\Product;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Services\Cart\CartService;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;


class Options extends Component
{
    public Product $product;
    public $selectedVariantId;
    public $selectedColorId;
    public $selectedSizeId;
    public $quantity = 1;
    public $inWishlist = false;

    // Available options based on selections
    public $availableColors;
    public $availableSizes;

    // All possible variants for the product
    private $allVariants;

    public function mount(Product $product)
    {
        \Log::info('Product Options Component Mount Started', [
            'product_id' => $product->id ?? 'null',
            'product_name' => $product->name ?? 'null',
            'user_id' => auth()->id(),
            'session_id' => session()->getId(),
            'timestamp' => now()->toISOString()
        ]);

        // EMERGENCY FIX: Add null checks and error handling
        if (!$product || !$product->exists) {
            \Log::error('Product Options Mount Failed - Invalid Product', [
                'product_id' => $product->id ?? 'null',
                'product_exists' => $product ? $product->exists : false
            ]);
            throw new \Exception('Product not found or invalid');
        }

        $this->product = $product->load('variants.color', 'variants.size');
        \Log::info('Product Loaded with Relationships', [
            'product_id' => $this->product->id,
            'variants_count' => $this->product->variants->count() ?? 0
        ]);

        // EMERGENCY FIX: Ensure variants relationship exists and is loaded
        $this->allVariants = $this->product->variants()->available()->with('color', 'size')->get();
        \Log::info('Product Variants Loaded', [
            'product_id' => $this->product->id,
            'variants_count' => $this->allVariants->count(),
            'variant_ids' => $this->allVariants->pluck('id')->toArray()
        ]);

        // CRITICAL FIX: Ensure allVariants is never null
        if (!$this->allVariants) {
            $this->allVariants = collect();
            \Log::warning('AllVariants was null, initialized empty collection', [
                'product_id' => $this->product->id
            ]);
        }

        // EMERGENCY FIX: Initialize collections even if empty to prevent null errors
        if ($this->allVariants && $this->allVariants->isNotEmpty()) {
            \Log::info('Processing Variants for Colors and Sizes', [
                'product_id' => $this->product->id,
                'variants_count' => $this->allVariants->count()
            ]);

            $this->availableColors = $this->allVariants->map(function($variant) {
                return $variant->color ?? null;
            })->filter(function($color) {
                return $color !== null && isset($color->id);
            })->unique('id');

            $this->availableSizes = $this->allVariants->map(function($variant) {
                return $variant->size ?? null;
            })->filter(function($size) {
                return $size !== null && isset($size->id);
            })->unique('id');

            \Log::info('Available Options Calculated', [
                'product_id' => $this->product->id,
                'available_colors_count' => $this->availableColors->count(),
                'available_colors' => $this->availableColors->pluck('name', 'id')->toArray(),
                'available_sizes_count' => $this->availableSizes->count(),
                'available_sizes' => $this->availableSizes->pluck('name', 'id')->toArray()
            ]);
        } else {
            $this->availableColors = collect();
            $this->availableSizes = collect();
            \Log::info('No Variants Available - Initialized Empty Collections', [
                'product_id' => $this->product->id
            ]);
        }

        // Ensure collections are never null
        if (!$this->availableColors) {
            $this->availableColors = collect();
        }
        if (!$this->availableSizes) {
            $this->availableSizes = collect();
        }

        // CRITICAL FIX: If product has only one variant type, auto-select if there's only one option
        if ($this->availableColors->count() === 1 && $this->availableSizes->isEmpty()) {
            // Color-only product with one color
            $firstColor = $this->availableColors->first();
            if ($firstColor && isset($firstColor->id)) {
                $this->selectColor($firstColor->id);
            }
        } elseif ($this->availableSizes->count() === 1 && $this->availableColors->isEmpty()) {
            // Size-only product with one size
            $firstSize = $this->availableSizes->first();
            if ($firstSize && isset($firstSize->id)) {
                $this->selectSize($firstSize->id);
            }
        }

        $this->checkWishlistStatus();
    }

    public function selectColor($colorId)
    {
        \Log::info('Color Selection Started', [
            'product_id' => $this->product->id,
            'previous_color_id' => $this->selectedColorId,
            'new_color_id' => $colorId,
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString()
        ]);

        // CRITICAL FIX: Validate colorId before proceeding
        if (!$colorId || !is_numeric($colorId)) {
            \Log::warning('Invalid Color ID Provided', [
                'product_id' => $this->product->id,
                'invalid_color_id' => $colorId
            ]);
            return;
        }

        $this->selectedColorId = $colorId;
        $this->selectedSizeId = null; // Reset size when color changes
        $this->selectedVariantId = null;

        \Log::info('Color Selection State Updated', [
            'product_id' => $this->product->id,
            'selected_color_id' => $this->selectedColorId,
            'reset_size_id' => true,
            'reset_variant_id' => true
        ]);

        // EMERGENCY FIX: Ensure allVariants is not null before using where()
        if (!$this->allVariants) {
            $this->allVariants = $this->product->variants()->available()->with('color', 'size')->get();
            // CRITICAL FIX: Ensure allVariants is never null
            if (!$this->allVariants) {
                $this->allVariants = collect();
            }
        }

        // Filter available sizes based on the selected color
        if ($this->allVariants && $this->allVariants->isNotEmpty()) {
            $this->availableSizes = $this->allVariants
                ->where('color_id', $this->selectedColorId)
                ->map(function($variant) {
                    return $variant->size ?? null;
                })
                ->filter(function($size) {
                    return $size !== null && isset($size->id);
                })
                ->unique('id');
        } else {
            $this->availableSizes = collect();
        }

        // CRITICAL FIX: Check if this color has variants without sizes (color-only variants)
        $colorOnlyVariant = null;
        if ($this->allVariants && $this->allVariants->isNotEmpty()) {
            $colorOnlyVariant = $this->allVariants->first(function ($variant) {
                return $variant->color_id == $this->selectedColorId && $variant->size_id === null;
            });
        }

        if ($colorOnlyVariant) {
            // If there's a color-only variant, select it immediately
            $this->selectedVariantId = $colorOnlyVariant->id;
            $this->quantity = 1;
        } elseif ($this->availableSizes->count() === 1) {
            // If there's only one size for this color, auto-select it
            $this->selectSize($this->availableSizes->first()->id);
        }
    }

    public function selectSize($sizeId)
    {
        // CRITICAL FIX: Validate sizeId before proceeding
        if (!$sizeId || !is_numeric($sizeId)) {
            return;
        }

        $this->selectedSizeId = $sizeId;

        // CRITICAL FIX: Find variant that matches the selection (handle both color+size and size-only variants)
        $variant = null;
        if ($this->allVariants && $this->allVariants->isNotEmpty()) {
            $variant = $this->allVariants->first(function ($variant) {
                // If we have both color and size selected
                if ($this->selectedColorId && $this->selectedSizeId) {
                    return $variant->color_id == $this->selectedColorId && $variant->size_id == $this->selectedSizeId;
                }
                // If we only have size selected (size-only variants)
                elseif ($this->selectedSizeId && !$this->selectedColorId) {
                    return $variant->size_id == $this->selectedSizeId && $variant->color_id === null;
                }
                return false;
            });
        }

        if ($variant) {
            $this->selectedVariantId = $variant->id;
            $this->quantity = 1; // Reset quantity
        } else {
            $this->selectedVariantId = null;
        }
    }

    public function getSelectedVariantProperty()
    {
        if (!$this->selectedVariantId || !is_numeric($this->selectedVariantId)) {
            return null;
        }

        try {
            // Load variant with relationships to ensure consistency
            $variant = ProductVariant::with(['color', 'size', 'product'])
                ->find($this->selectedVariantId);

            // Ensure the variant belongs to this product
            if ($variant && $variant->product_id !== $this->product->id) {
                \Log::warning('Variant does not belong to product', [
                    'variant_id' => $this->selectedVariantId,
                    'variant_product_id' => $variant->product_id,
                    'expected_product_id' => $this->product->id
                ]);
                return null;
            }

            return $variant;
        } catch (\Exception $e) {
            \Log::error('Error loading selected variant', [
                'variant_id' => $this->selectedVariantId,
                'product_id' => $this->product->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    public function checkWishlistStatus()
    {
        if (Auth::check()) {
            $this->inWishlist = Auth::user()->wishlist()->where('product_id', $this->product->id)->exists();
        }
    }

    public function toggleWishlist()
    {
        if (!Auth::check()) {
            return $this->redirect(route('login'));
        }

        if ($this->inWishlist) {
            Auth::user()->wishlist()->where('product_id', $this->product->id)->delete();
            $this->inWishlist = false;
            $this->dispatch('toast', message: 'Removed from wishlist.', type: 'info');
        } else {
            Auth::user()->wishlist()->create(['product_id' => $this->product->id]);
            $this->inWishlist = true;
            $this->dispatch('toast', message: 'Added to wishlist!', type: 'success');
        }
    }

    public function addToCart()
    {
        \Log::info('Add to Cart Started', [
            'product_id' => $this->product->id,
            'product_name' => $this->product->name,
            'quantity' => $this->quantity,
            'selected_variant_id' => $this->selectedVariantId,
            'selected_color_id' => $this->selectedColorId,
            'selected_size_id' => $this->selectedSizeId,
            'user_id' => auth()->id(),
            'session_id' => session()->getId(),
            'timestamp' => now()->toISOString()
        ]);

        if (!Auth::check()) {
            \Log::warning('Add to Cart Failed - User Not Authenticated', [
                'product_id' => $this->product->id,
                'session_id' => session()->getId()
            ]);
            $this->dispatch('toast', message: 'Please login to add items to your cart.', type: 'info');
            return $this->redirect(route('login'));
        }

        $variant = null;
        \Log::info('Processing Variant Selection for Cart', [
            'product_id' => $this->product->id,
            'has_variants' => $this->product->variants->isNotEmpty(),
            'variants_count' => $this->product->variants->count(),
            'selected_variant_id' => $this->selectedVariantId
        ]);

        if ($this->product->variants->isNotEmpty()) {
            if (!$this->selectedVariantId) {
                \Log::warning('Add to Cart Failed - No Variant Selected', [
                    'product_id' => $this->product->id,
                    'variants_count' => $this->product->variants->count()
                ]);
                $this->dispatch('toast', message: 'Please select a variant.', type: 'error');
                return;
            }
            $variant = $this->product->variants()->with(['size', 'color'])->find($this->selectedVariantId);
            \Log::info('Variant Retrieved for Cart', [
                'product_id' => $this->product->id,
                'variant_id' => $variant ? $variant->id : null,
                'variant_found' => $variant !== null
            ]);

            // Ensure variant exists and is available
            if (!$variant) {
                $this->dispatch('toast', message: 'Selected variant is not available.', type: 'error');
                return;
            }

            if (!$variant->isAvailable()) {
                $this->dispatch('toast', message: 'Selected variant is not available.', type: 'error');
                return;
            }

            // Check stock
            if ($variant->stock_quantity < $this->quantity) {
                $this->dispatch('toast', message: 'Not enough stock available.', type: 'error');
                return;
            }
        } else {
            // Check product stock for non-variant products
            if ($this->product->stock < $this->quantity) {
                $this->dispatch('toast', message: 'Not enough stock available.', type: 'error');
                return;
            }
        }

        // Use CartService to add item
        $cartService = app(CartService::class);
        $cartService->add($this->product, $this->quantity, $variant);

        // Get updated cart count for proper counter updates
        $cart = session()->get('cart', []);
        $cartCount = count($cart);

        // Dispatch multiple events to ensure cart counter updates properly
        $this->dispatch('cartUpdated'); // For updating cart count in navbar, etc.
        $this->dispatch('cart-updated-total', $cartCount); // For cart counter component

        $variantText = $variant ? " ({$variant->display_name})" : '';
        $this->dispatch('toast', message: "Product{$variantText} added to cart!", type: 'success');
    }

    public function incrementQuantity()
    {
        \Log::info('Increment Quantity Started', [
            'product_id' => $this->product->id,
            'current_quantity' => $this->quantity,
            'selected_variant_id' => $this->selectedVariantId,
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString()
        ]);

        try {
            // Get the selected variant safely
            $selectedVariant = $this->getSelectedVariantProperty();
            $stock = $selectedVariant ? $selectedVariant->stock_quantity : $this->product->stock;
            $stock = $stock ?? 0; // Ensure stock is never null

            \Log::info('Stock Check for Increment', [
                'product_id' => $this->product->id,
                'selected_variant_id' => $this->selectedVariantId,
                'current_quantity' => $this->quantity,
                'available_stock' => $stock,
                'has_selected_variant' => $selectedVariant !== null
            ]);

            if ($this->quantity < $stock) {
                $this->quantity++;
                \Log::info('Quantity Incremented Successfully', [
                    'product_id' => $this->product->id,
                    'new_quantity' => $this->quantity,
                    'remaining_stock' => $stock - $this->quantity
                ]);
            } else {
                \Log::warning('Maximum Stock Reached on Increment', [
                    'product_id' => $this->product->id,
                    'current_quantity' => $this->quantity,
                    'max_stock' => $stock
                ]);
                $this->dispatch('toast', message: 'Maximum stock reached.', type: 'warning');
            }
        } catch (\Exception $e) {
            \Log::error('Error incrementing quantity', [
                'product_id' => $this->product->id,
                'variant_id' => $this->selectedVariantId,
                'current_quantity' => $this->quantity,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    public function decrementQuantity()
    {
        \Log::info('Decrement Quantity Started', [
            'product_id' => $this->product->id,
            'current_quantity' => $this->quantity,
            'selected_variant_id' => $this->selectedVariantId,
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString()
        ]);

        if ($this->quantity > 1) {
            $this->quantity--;
            \Log::info('Quantity Decremented Successfully', [
                'product_id' => $this->product->id,
                'new_quantity' => $this->quantity
            ]);
        } else {
            \Log::info('Minimum Quantity Reached on Decrement', [
                'product_id' => $this->product->id,
                'current_quantity' => $this->quantity
            ]);
            $this->dispatch('toast', message: 'Minimum quantity is 1.', type: 'info');
        }
    }

    public function render()
    {
        return view('livewire.product.options');
    }
}
